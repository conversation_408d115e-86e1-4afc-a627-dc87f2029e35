using Microsoft.Graph;
using Microsoft.EntityFrameworkCore;
using AccureMD.TeamsBot.Models;
using AccureMD.TeamsBot.Data;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace AccureMD.TeamsBot.Services;

public class MeetingService
{
    private readonly AuthenticationService _authService;
    private readonly RecordingService _recordingService;
    private readonly TranscriptionService _transcriptionService;
    private readonly StorageService _storageService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<MeetingService> _logger;
    private readonly Dictionary<string, MeetingModel> _activeMeetings; // Keep for active session tracking

    public MeetingService(
        AuthenticationService authService,
        RecordingService recordingService,
        TranscriptionService transcriptionService,
        StorageService storageService,
        ApplicationDbContext dbContext,
        ILogger<MeetingService> logger)
    {
        _authService = authService;
        _recordingService = recordingService;
        _transcriptionService = transcriptionService;
        _storageService = storageService;
        _dbContext = dbContext;
        _logger = logger;
        _activeMeetings = new Dictionary<string, MeetingModel>();
    }

    public async Task<MeetingResponse> JoinMeetingAsGuestAsync(string meetingUrl, string userId)
    {
        try
        {
            _logger.LogInformation($"Attempting to join meeting: {meetingUrl} for user: {userId}");

            // Extract meeting ID from URL
            var meetingId = ExtractMeetingIdFromUrl(meetingUrl);
            if (string.IsNullOrEmpty(meetingId))
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Invalid meeting URL format"
                };
            }

            // Get Graph client for the user
            var graphClient = await _authService.GetGraphClientForUserAsync(userId);
            if (graphClient == null)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "User not authenticated. Please authenticate first."
                };
            }

            // Create a meeting object to track
            var meeting = new MeetingModel
            {
                Id = meetingId,
                MeetingUrl = meetingUrl,
                Title = "AccureMD Recording Session",
                StartTime = DateTime.UtcNow,
                Status = "Active",
                OrganizerId = userId,
                IsRecording = false,
                IsTranscribing = false
            };

            // Store the active meeting in cache and database
            _activeMeetings[meetingId] = meeting;
            await _storageService.SaveRecordingMetadataAsync(meeting);

            // In a real implementation, this would use Microsoft Graph APIs
            // to join the meeting as a bot participant
            await SimulateJoinMeetingAsync(meeting);

            _logger.LogInformation($"Successfully joined meeting {meetingId}");

            return new MeetingResponse
            {
                Success = true,
                Message = "Successfully joined meeting as AccureMD Bot",
                MeetingId = meetingId,
                JoinUrl = meetingUrl
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to join meeting: {meetingUrl}");
            return new MeetingResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<MeetingResponse> StartRecordingAsync(string meetingId)
    {
        try
        {
            if (!_activeMeetings.TryGetValue(meetingId, out var meeting))
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Meeting not found or not active"
                };
            }

            if (meeting.IsRecording)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Recording is already active"
                };
            }

            // Start recording
            var recordingResult = await _recordingService.StartRecordingAsync(meetingId, meeting.MeetingUrl);
            if (!recordingResult.Success)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = $"Failed to start recording: {recordingResult.Message}"
                };
            }

            // Start transcription
            var transcriptionResult = await _transcriptionService.StartLiveTranscriptionAsync(meetingId);
            if (!transcriptionResult.Success)
            {
                _logger.LogWarning($"Transcription failed to start for meeting {meetingId}: {transcriptionResult.Message}");
            }

            // Update meeting status
            meeting.IsRecording = true;
            meeting.IsTranscribing = transcriptionResult.Success;
            meeting.RecordingPath = recordingResult.RecordingPath ?? "";

            _logger.LogInformation($"Started recording for meeting {meetingId}");

            return new MeetingResponse
            {
                Success = true,
                Message = "Recording started successfully",
                MeetingId = meetingId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to start recording for meeting {meetingId}");
            return new MeetingResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<MeetingResponse> StopRecordingAsync(string meetingId)
    {
        try
        {
            if (!_activeMeetings.TryGetValue(meetingId, out var meeting))
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Meeting not found or not active"
                };
            }

            if (!meeting.IsRecording)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "No active recording found"
                };
            }

            // Stop recording
            var recordingResult = await _recordingService.StopRecordingAsync(meetingId);
            if (!recordingResult.Success)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = $"Failed to stop recording: {recordingResult.Message}"
                };
            }

            // Stop transcription
            var transcriptionResult = await _transcriptionService.StopLiveTranscriptionAsync(meetingId);

            // Update meeting status
            meeting.IsRecording = false;
            meeting.IsTranscribing = false;
            meeting.EndTime = DateTime.UtcNow;
            meeting.Status = "Completed";
            meeting.TranscriptPath = transcriptionResult.TranscriptPath ?? "";

            _logger.LogInformation($"Stopped recording for meeting {meetingId}");

            return new MeetingResponse
            {
                Success = true,
                Message = "Recording stopped and saved successfully",
                MeetingId = meetingId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to stop recording for meeting {meetingId}");
            return new MeetingResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<string> GetBotStatusAsync()
    {
        await Task.CompletedTask;

        var activeCount = _activeMeetings.Count(m => m.Value.Status == "Active");
        var recordingCount = _activeMeetings.Count(m => m.Value.IsRecording);

        return $"Online | Active Meetings: {activeCount} | Recording: {recordingCount}";
    }

    public async Task<MeetingModel?> GetMeetingAsync(string meetingId)
    {
        await Task.CompletedTask;
        _activeMeetings.TryGetValue(meetingId, out var meeting);
        return meeting;
    }

    public async Task<List<MeetingModel>> GetActiveMeetingsAsync(string userId)
    {
        await Task.CompletedTask;
        return _activeMeetings.Values
            .Where(m => m.OrganizerId == userId && m.Status == "Active")
            .ToList();
    }

    private string ExtractMeetingIdFromUrl(string meetingUrl)
    {
        try
        {
            // Extract meeting ID from Teams meeting URL
            // Teams URLs typically contain the meeting ID in various formats
            var patterns = new[]
            {
                @"meetup-join/([a-zA-Z0-9\-_]+)",
                @"conversations/([a-zA-Z0-9\-_]+)",
                @"thread\.v2/([a-zA-Z0-9\-_]+)",
                @"meetingId=([a-zA-Z0-9\-_]+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(meetingUrl, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            // If no pattern matches, generate a unique ID based on the URL
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(meetingUrl))
                .Replace("+", "-").Replace("/", "_").Replace("=", "")[..16];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to extract meeting ID from URL: {meetingUrl}");
            return Guid.NewGuid().ToString("N")[..16];
        }
    }

    private async Task SimulateJoinMeetingAsync(MeetingModel meeting)
    {
        // This method simulates joining a meeting
        // In a real implementation, this would use the Microsoft Graph API
        // or Teams SDK to join the meeting as a bot participant

        await Task.Delay(1000); // Simulate network delay

        _logger.LogInformation($"Bot joined meeting: {meeting.Id}");

        // Add bot as a participant
        meeting.Participants.Add("AccureMD Bot");
    }

    public async Task LeaveMeetingAsync(string meetingId)
    {
        try
        {
            if (_activeMeetings.TryGetValue(meetingId, out var meeting))
            {
                // Stop any active recording/transcription
                if (meeting.IsRecording)
                {
                    await StopRecordingAsync(meetingId);
                }

                // Mark meeting as completed
                meeting.Status = "Completed";
                meeting.EndTime = DateTime.UtcNow;

                _logger.LogInformation($"Left meeting: {meetingId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to leave meeting {meetingId}");
        }
    }
}