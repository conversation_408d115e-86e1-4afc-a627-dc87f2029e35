using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Models;

namespace AccureMD.TeamsBot.Controllers;

[Route("api/auth")]
[ApiController]
public class AuthController : ControllerBase
{
    private readonly AuthenticationService _authService;
    private readonly ILogger<AuthController> _logger;
    private readonly IConfiguration _configuration;

    public AuthController(AuthenticationService authService, ILogger<AuthController> logger, IConfiguration configuration)
    {
        _authService = authService;
        _logger = logger;
        _configuration = configuration;
    }

    [HttpGet("login")]
    public async Task<IActionResult> InitiateLogin([FromQuery] string userId, [FromQuery] string redirectUri)
    {
        try
        {
            _logger.LogInformation("Login request received - UserId: {UserId}, RedirectUri: {RedirectUri}", userId, redirectUri);

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(redirectUri))
            {
                _logger.LogWarning("Missing required parameters - UserId: {HasUserId}, RedirectUri: {HasRedirectUri}",
                    !string.IsNullOrEmpty(userId), !string.IsNullOrEmpty(redirectUri));
                return BadRequest("UserId and redirectUri are required");
            }

            var result = await _authService.InitiateAuthenticationAsync(userId, redirectUri);

            _logger.LogInformation("Login initiation result - Success: {Success}, Message: {Message}",
                result.Success, result.Message);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating authentication");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("start-teams-login")]
    public async Task<IActionResult> StartTeamsLogin([FromQuery] string userId, [FromQuery] string redirectUri)
    {
        try
        {
            _logger.LogInformation("Teams login redirect request received - UserId: {UserId}, RedirectUri: {RedirectUri}", userId, redirectUri);

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(redirectUri))
            {
                _logger.LogWarning("Missing required parameters for Teams login - UserId: {HasUserId}, RedirectUri: {HasRedirectUri}",
                    !string.IsNullOrEmpty(userId), !string.IsNullOrEmpty(redirectUri));
                return BadRequest("UserId and redirectUri are required");
            }

            var result = await _authService.InitiateAuthenticationAsync(userId, redirectUri);

            _logger.LogInformation("Teams login initiation result - Success: {Success}, Message: {Message}",
                result.Success, result.Message);

            if (result.Success && !string.IsNullOrEmpty(result.AuthUrl))
            {
                _logger.LogInformation("Redirecting to Microsoft login URL: {AuthUrl}", result.AuthUrl);
                return Redirect(result.AuthUrl);
            }
            else
            {
                _logger.LogError("Failed to generate auth URL for Teams login - Message: {Message}", result.Message);
                return BadRequest($"Failed to initiate authentication: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating Teams authentication redirect");
            return StatusCode(500, $"Error initiating authentication: {ex.Message}");
        }
    }

    [HttpPost("callback")]
    public async Task<IActionResult> AuthCallback([FromBody] AuthenticationRequest request)
    {
        try
        {
            _logger.LogInformation("Auth callback received - Code: {HasCode}, State: {State}, RedirectUri: {RedirectUri}",
                !string.IsNullOrEmpty(request.Code), request.State, request.RedirectUri);

            if (string.IsNullOrEmpty(request.Code))
            {
                _logger.LogWarning("Authorization code is missing in callback request");
                return BadRequest("Authorization code is required");
            }

            var result = await _authService.HandleAuthenticationCallbackAsync(
                request.Code,
                request.State,
                request.RedirectUri);

            _logger.LogInformation("Auth callback result - Success: {Success}, Message: {Message}",
                result.Success, result.Message);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling auth callback");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("callback")]
    public IActionResult AuthCallbackGet([FromQuery] string code, [FromQuery] string state, [FromQuery] string error, [FromQuery] string error_description)
    {
        try
        {
            _logger.LogInformation($"GET auth callback - Code: {code?.Substring(0, Math.Min(10, code?.Length ?? 0))}..., State: {state}, Error: {error}");

            if (!string.IsNullOrEmpty(error))
            {
                _logger.LogError($"OAuth error: {error} - {error_description}");
                return BadRequest(new { error = error, error_description = error_description });
            }

            if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(state))
            {
                return BadRequest("Authorization code and state are required");
            }

            // For GET requests, we'll redirect to the auth-callback.html page with the parameters
            // This allows the JavaScript to handle the callback properly
            var redirectUrl = $"/html/auth-callback.html?code={code}&state={state}";
            return Redirect(redirectUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GET auth callback");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("status/{userId}")]
    public async Task<IActionResult> GetAuthStatus(string userId)
    {
        try
        {
            var status = await _authService.GetAuthenticationStatusAsync(userId);
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auth status");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("logout/{userId}")]
    public async Task<IActionResult> Logout(string userId)
    {
        try
        {
            await _authService.ClearUserSessionAsync(userId);
            return Ok(new { success = true, message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("establish-session")]
    public async Task<IActionResult> EstablishSession([FromBody] EstablishSessionRequest request)
    {
        try
        {
            _logger.LogInformation("Establish session request received - AccessToken: {HasAccessToken}, State: {State}",
                !string.IsNullOrEmpty(request.AccessToken), request.State);

            if (string.IsNullOrEmpty(request.AccessToken))
            {
                _logger.LogWarning("Access token is missing in establish session request");
                return BadRequest("Access token is required");
            }

            var result = await _authService.EstablishSessionWithTokenAsync(
                request.AccessToken,
                request.State);

            _logger.LogInformation("Establish session result - Success: {Success}, Message: {Message}",
                result.Success, result.Message);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error establishing session");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("test")]
    public IActionResult TestConfiguration()
    {
        try
        {
            var config = new
            {
                AppId = _configuration["Teams:AppId"],
                TenantId = _configuration["Teams:TenantId"],
                HasAppSecret = !string.IsNullOrEmpty(_configuration["Teams:AppSecret"]),
                BaseUrl = _configuration["BaseUrl"],
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                Scopes = new[]
                {
                    "https://graph.microsoft.com/User.Read",
                    "https://graph.microsoft.com/OnlineMeetings.ReadWrite",
                    "https://graph.microsoft.com/Calendars.Read"
                }
            };

            return Ok(new { success = true, configuration = config });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing configuration");
            return StatusCode(500, new { error = ex.Message });
        }
    }
}