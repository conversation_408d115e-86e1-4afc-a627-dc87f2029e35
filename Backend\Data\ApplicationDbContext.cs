
using AccureMD.TeamsBot.Models;
using Microsoft.EntityFrameworkCore;

namespace AccureMD.TeamsBot.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<AuthenticationModel> Users { get; set; }
        public DbSet<MeetingModel> Meetings { get; set; }
        public DbSet<TranscriptModel> Transcripts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Users table
            modelBuilder.Entity<AuthenticationModel>(entity =>
            {
                entity.ToTable("users");
                entity.HasKey(e => e.UserId);
                entity.Property(e => e.UserId).HasColumnName("user_id");
                entity.Property(e => e.UserName).HasColumnName("user_name");
                entity.Property(e => e.Email).HasColumnName("email");
                entity.Property(e => e.AccessToken).HasColumnName("access_token");
                entity.Property(e => e.RefreshToken).HasColumnName("refresh_token");
                entity.Property(e => e.TokenExpiry).HasColumnName("token_expiry");
                entity.Property(e => e.IsAuthenticated).HasColumnName("is_authenticated");
                entity.Property(e => e.Permissions).HasColumnName("permissions");

                // Configure unique constraint on email
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configure Meetings table
            modelBuilder.Entity<MeetingModel>(entity =>
            {
                entity.ToTable("meetings");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.MeetingUrl).HasColumnName("meeting_url");
                entity.Property(e => e.Title).HasColumnName("title");
                entity.Property(e => e.StartTime).HasColumnName("start_time");
                entity.Property(e => e.EndTime).HasColumnName("end_time");
                entity.Property(e => e.Status).HasColumnName("status");
                entity.Property(e => e.OrganizerId).HasColumnName("organizer_id");
                entity.Property(e => e.Participants).HasColumnName("participants");
                entity.Property(e => e.RecordingPath).HasColumnName("recording_path");
                entity.Property(e => e.TranscriptPath).HasColumnName("transcript_path");
                entity.Property(e => e.IsRecording).HasColumnName("is_recording");
                entity.Property(e => e.IsTranscribing).HasColumnName("is_transcribing");

                // Configure foreign key relationship
                entity.HasOne(e => e.Organizer)
                    .WithMany(u => u.OrganizedMeetings)
                    .HasForeignKey(e => e.OrganizerId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Transcripts table
            modelBuilder.Entity<TranscriptModel>(entity =>
            {
                entity.ToTable("transcripts");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.MeetingId).HasColumnName("meeting_id");
                entity.Property(e => e.Timestamp).HasColumnName("timestamp");
                entity.Property(e => e.SpeakerId).HasColumnName("speaker_id");
                entity.Property(e => e.SpeakerName).HasColumnName("speaker_name");
                entity.Property(e => e.Text).HasColumnName("text");
                entity.Property(e => e.Confidence).HasColumnName("confidence");
                entity.Property(e => e.OffsetFromStart).HasColumnName("offset_from_start");

                // Configure foreign key relationship
                entity.HasOne(e => e.Meeting)
                    .WithMany(m => m.Transcripts)
                    .HasForeignKey(e => e.MeetingId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Create indexes for performance (matching your schema)
            modelBuilder.Entity<MeetingModel>()
                .HasIndex(e => e.OrganizerId)
                .HasDatabaseName("idx_meetings_organizer_id");

            modelBuilder.Entity<TranscriptModel>()
                .HasIndex(e => e.MeetingId)
                .HasDatabaseName("idx_transcripts_meeting_id");
        }
    }
}
