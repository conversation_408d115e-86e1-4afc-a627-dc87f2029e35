using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

[Table("meetings")]
public class MeetingModel
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = string.Empty;

    [Column("meeting_url")]
    public string? MeetingUrl { get; set; }

    [Column("title")]
    public string? Title { get; set; }

    [Column("start_time")]
    public DateTime? StartTime { get; set; }

    [Column("end_time")]
    public DateTime? EndTime { get; set; }

    [Column("status")]
    public string? Status { get; set; } = "Pending"; // Pending, Active, Completed, Error

    [Column("organizer_id")]
    public string? OrganizerId { get; set; }

    [Column("participants")]
    public List<string> Participants { get; set; } = new();

    [Column("recording_path")]
    public string? RecordingPath { get; set; }

    [Column("transcript_path")]
    public string? TranscriptPath { get; set; }

    [Column("is_recording")]
    public bool IsRecording { get; set; } = false;

    [Column("is_transcribing")]
    public bool IsTranscribing { get; set; } = false;

    // Navigation properties
    [ForeignKey("OrganizerId")]
    public virtual AuthenticationModel? Organizer { get; set; }

    public virtual ICollection<TranscriptModel> Transcripts { get; set; } = new List<TranscriptModel>();
}

public class MeetingJoinRequest
{
    [JsonPropertyName("meetingUrl")]
    public string MeetingUrl { get; set; } = string.Empty;

    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("displayName")]
    public string DisplayName { get; set; } = "AccureMD Bot";
}

public class MeetingResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("meetingId")]
    public string MeetingId { get; set; } = string.Empty;

    [JsonPropertyName("joinUrl")]
    public string JoinUrl { get; set; } = string.Empty;
}