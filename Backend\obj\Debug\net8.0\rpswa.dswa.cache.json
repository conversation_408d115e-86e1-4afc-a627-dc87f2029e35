{"GlobalPropertiesHash": "ITtAFkNiSgdhUb2BwCQUf6iyFsK/CwOgeB9JuCIv+uU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=", "T/CbM5YThBQvWLE/3nQi4md7nQ/7gtA0XZxbhVjjr7U=", "4WV+ZFXdO3BJ364M0CWxeOArRuETGCnkAO87wyQaM6Q=", "FU2jzAoNm6iBK+2mM+KL2Zrtp2luW6iNX/AGlgY1yYo=", "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=", "0DvZrFHfqgSlSTNIS5G9bmf4AdWKJurkDahoHvQXZGs=", "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=", "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=", "kJTXQF6cHP5f/JnQjk/83CqY9pKydomxLpcty3UZ3OE=", "9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=", "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=", "PfSy9iEmWCZmTlXtjhNtqpGcckvmuVG0YKHQNxHLUyE=", "qExUL3YSW7fshme09gIbW1jaizMAfi1epm7bwsSMPZI=", "qFlo+EWtmLm+4U7Gxv51oXPqVQeMlk06V4S/Hpw/l8A=", "UTEyJSx8XFmWjf5JUjW/XtjRefqNNzhYySzxSve2jeY=", "D2g7T6cYFSMam4Nw406jLXeKkUxUBT00xF38I5wJRlg=", "/7GX4RS3So8pUTxEVkxZJeuPpn9k8sRR17PhYawpsig=", "5fqYcXSVPxfvvkXrisFy9SVmWO4ExsNRiwZT6DQU3LY=", "2zs207MjfPqE0Ayb1vqu1rdEMT8ExxjdHtdfWC0eXs8=", "akVdw0AWR8/Dxc+2891pUXjkR4Gt+NjB8AXQc770XJk=", "GwozmaXb2F/ZHQutXlzbKGTOj+YfjB3jZ8YskJERr5A=", "o8yfddgVdsjpuf1uhpjqDxApXitx0E/pex6zctd+AYA="], "CachedAssets": {"krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vwl5012ydz", "Integrity": "0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 11424, "LastWriteTime": "2025-08-08T19:01:32.3671149+00:00"}, "T/CbM5YThBQvWLE/3nQi4md7nQ/7gtA0XZxbhVjjr7U=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ere7y5064t", "Integrity": "elxfXvf7dlkoGnW2sOoZOlyOMW81PjUeeu0jeq+uqmw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 26875, "LastWriteTime": "2025-08-09T13:35:17.5411793+00:00"}, "4WV+ZFXdO3BJ364M0CWxeOArRuETGCnkAO87wyQaM6Q=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uorbu8vuak", "Integrity": "7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 5567, "LastWriteTime": "2025-08-08T20:07:03.6376846+00:00"}, "FU2jzAoNm6iBK+2mM+KL2Zrtp2luW6iNX/AGlgY1yYo=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zbbusfory5", "Integrity": "3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-test.html", "FileLength": 11310, "LastWriteTime": "2025-08-09T13:11:16.5535654+00:00"}, "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inynrhuf2", "Integrity": "gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4092, "LastWriteTime": "2025-08-08T17:45:39.2143566+00:00"}, "0DvZrFHfqgSlSTNIS5G9bmf4AdWKJurkDahoHvQXZGs=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h6nvgw2bxq", "Integrity": "GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6691, "LastWriteTime": "2025-08-08T20:14:05.6425962+00:00"}, "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26.6394667+00:00"}, "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34.6547862+00:00"}, "kJTXQF6cHP5f/JnQjk/83CqY9pKydomxLpcty3UZ3OE=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xoy9uerqmq", "Integrity": "H+Jbqu+7k9v13fAuSbMp5E5JH70AsnnjTuJsOKkaSBE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 20472, "LastWriteTime": "2025-08-09T13:10:32.2684072+00:00"}, "9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cztlu2xxj7", "Integrity": "+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 6390, "LastWriteTime": "2025-08-08T19:44:49.8475713+00:00"}, "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16.6719653+00:00"}, "PfSy9iEmWCZmTlXtjhNtqpGcckvmuVG0YKHQNxHLUyE=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-teams-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r54zswcdn2", "Integrity": "zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-teams-auth.html", "FileLength": 9705, "LastWriteTime": "2025-08-08T20:45:49.2592213+00:00"}}, "CachedCopyCandidates": {}}